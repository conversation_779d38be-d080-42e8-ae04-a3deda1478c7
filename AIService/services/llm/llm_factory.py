"""
LLM Factory for creating LLM instances based on configuration
"""

import os
from typing import Optional, Any, Dict
from langchain_ollama import Chat<PERSON><PERSON><PERSON>
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.language_models.chat_models import BaseChatModel
from config import settings
from loguru import logger


class LLMFactory:
    """Factory class for creating LLM instances based on configuration"""
    
    @staticmethod
    def create_llm(
        provider: Optional[str] = None,
        model: Optional[str] = None,
        temperature: float = 0.0,
        **kwargs
    ) -> BaseChatModel:
        """
        Create an LLM instance based on the provider configuration
        
        Args:
            provider: LLM provider ("ollama" or "gemini"). If None, uses config default
            model: Model name. If None, uses config default
            temperature: Temperature for generation
            **kwargs: Additional arguments for the LLM
            
        Returns:
            BaseChatModel: Configured LLM instance
        """
        # Use config defaults if not provided
        provider = provider or settings.llm_provider
        model = model or settings.llm_model
        
        logger.info(f"Creating LLM instance: provider={provider}, model={model}")
        
        if provider.lower() == "gemini":
            return LLMFactory._create_gemini_llm(model, temperature, **kwargs)
        elif provider.lower() == "ollama":
            return LLMFactory._create_ollama_llm(model, temperature, **kwargs)
        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")
    
    @staticmethod
    def _create_gemini_llm(
        model: str,
        temperature: float,
        **kwargs
    ) -> ChatGoogleGenerativeAI:
        """Create a Gemini LLM instance"""
        
        if not settings.gemini_api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required for Gemini provider")
        
        # Default Gemini configuration
        gemini_config = {
            "model": model,
            "temperature": temperature,
            "google_api_key": settings.gemini_api_key,
        }
        
        # Override with any provided kwargs
        gemini_config.update(kwargs)
        
        logger.info(f"Creating Gemini LLM with model: {model}")
        return ChatGoogleGenerativeAI(**gemini_config)
    
    @staticmethod
    def _create_ollama_llm(
        model: str,
        temperature: float,
        **kwargs
    ) -> ChatOllama:
        """Create an Ollama LLM instance"""
        
        # Default Ollama configuration
        ollama_config = {
            "model": model,
            "temperature": temperature,
            "base_url": kwargs.get("base_url", "http://ai.kontratar.com:11434"),
        }
        
        # Override with any provided kwargs
        ollama_config.update(kwargs)
        
        logger.info(f"Creating Ollama LLM with model: {model}")
        return ChatOllama(**ollama_config)
    
    @staticmethod
    def get_default_llm(**kwargs) -> BaseChatModel:
        """Get the default LLM instance based on current configuration"""
        return LLMFactory.create_llm(**kwargs)


# Convenience function for backward compatibility
def get_llm(
    provider: Optional[str] = None,
    model: Optional[str] = None,
    temperature: float = 0.0,
    **kwargs
) -> BaseChatModel:
    """
    Convenience function to get an LLM instance
    
    Args:
        provider: LLM provider ("ollama" or "gemini")
        model: Model name
        temperature: Temperature for generation
        **kwargs: Additional arguments for the LLM
        
    Returns:
        BaseChatModel: Configured LLM instance
    """
    return LLMFactory.create_llm(provider, model, temperature, **kwargs)
