#!/usr/bin/env python3
"""
Test script to verify Gemini integration across different services
"""

import asyncio
from dotenv import load_dotenv
from services.llm.llm_factory import get_llm
from services.proposal.chat_service import ChatService
from services.llm.ollama_llm import OllamaLLMService

load_dotenv()


async def test_llm_factory():
    """Test the LLM factory directly"""
    print("🧪 Testing LLM Factory...")
    
    try:
        llm = get_llm()
        response = await llm.ainvoke("What is the capital of France?")
        print(f"✅ LLM Factory Response: {response.content}")
        return True
    except Exception as e:
        print(f"❌ LLM Factory Error: {e}")
        return False


async def test_chat_service():
    """Test the ChatService with new LLM"""
    print("\n🧪 Testing ChatService...")
    
    try:
        chat_service = ChatService()
        # Simple test without ChromaDB dependency
        response = await chat_service.llm.ainvoke("What is 2+2?")
        print(f"✅ ChatService Response: {response.content}")
        return True
    except Exception as e:
        print(f"❌ ChatService Error: {e}")
        return False


async def test_ollama_llm_service():
    """Test the OllamaLLMService (now using factory)"""
    print("\n🧪 Testing OllamaLLMService...")
    
    try:
        ollama_service = OllamaLLMService()
        response = await ollama_service.llm.ainvoke("Hello from OllamaLLMService!")
        print(f"✅ OllamaLLMService Response: {response.content}")
        return True
    except Exception as e:
        print(f"❌ OllamaLLMService Error: {e}")
        return False


def test_provider_switching():
    """Test switching between providers"""
    print("\n🧪 Testing Provider Switching...")
    
    try:
        # Test Gemini
        gemini_llm = get_llm(provider="gemini", model="gemini-1.5-flash")
        print(f"✅ Gemini LLM created: {type(gemini_llm).__name__}")
        
        # Test Ollama (if available)
        try:
            ollama_llm = get_llm(provider="ollama", model="gemma3:27b")
            print(f"✅ Ollama LLM created: {type(ollama_llm).__name__}")
        except Exception as e:
            print(f"⚠️  Ollama LLM not available (expected): {e}")
        
        return True
    except Exception as e:
        print(f"❌ Provider Switching Error: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Starting Gemini Integration Tests...\n")
    
    results = []
    
    # Test LLM Factory
    results.append(await test_llm_factory())
    
    # Test ChatService
    results.append(await test_chat_service())
    
    # Test OllamaLLMService
    results.append(await test_ollama_llm_service())
    
    # Test Provider Switching
    results.append(test_provider_switching())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Gemini integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
